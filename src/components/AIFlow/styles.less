.ctn{
    width: 100%;
    height: 100vh;
    background: #ffffff;
    position: relative;

    .reactflowWrapper {
        width: 100%;
        height: 100%;
    }

    // 隐藏 react-flow 右下角的 logo
    //:global(.react-flow__attribution) {
    //    display: none !important;
    //}

    // 工具面板触发器样式
    .toolTrigger {
        position: fixed;
        bottom: 30px;
        left: 50%;
        transform: translateX(-50%);
        background: #1890ff;
        color: white;
        padding: 12px 20px;
        border-radius: 25px;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 8px;
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        transition: all 0.3s ease;
        z-index: 1000;
        user-select: none;
        font-size: 14px;
        font-weight: 500;

        &:hover {
            background: #40a9ff;
            transform: translateX(-50%) translateY(-2px);
            box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
        }

        &.active {
            background: #096dd9;
            transform: translateX(-50%) translateY(-1px);
        }

        span {
            font-size: 14px;
        }
    }

    // 工具面板容器样式
    .toolPanelContainer {
        position: fixed;
        bottom: 90px;
        left: 50%;
        transform: translateX(-50%);
        width: 35%;
        max-width: 600px;
        min-width: 400px;
        max-height: 70vh;
        overflow-y: auto;
        z-index: 999;
        animation: slideUp 0.3s ease-out;

        // 滑入动画
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateX(-50%) translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateX(-50%) translateY(0);
            }
        }

        // 自定义滚动条
        &::-webkit-scrollbar {
            width: 6px;
        }

        &::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;

            &:hover {
                background: #a8a8a8;
            }
        }
    }

    // 响应式设计
    @media (max-width: 1200px) {
        .toolPanelContainer {
            width: 45%;
            min-width: 350px;
        }
    }

    @media (max-width: 768px) {
        .toolPanelContainer {
            width: 90%;
            min-width: 300px;
            left: 5%;
            transform: none;
        }

        .toolTrigger {
            bottom: 20px;
            padding: 10px 16px;
            font-size: 13px;

            span {
                font-size: 13px;
            }
        }
    }
}
